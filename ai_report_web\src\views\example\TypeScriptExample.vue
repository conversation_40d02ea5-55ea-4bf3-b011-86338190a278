<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ExampleComponent from '@/components/ExampleComponent.vue'
import { useUserStore } from '@/store/modules/user'
import { getAllTags } from '@/api/tag'
import { Tag } from '@/types'

// 使用 Pinia store
const userStore = useUserStore()

// 基本类型
const count = ref<number>(0)
const message = ref<string>('Hello TypeScript!')
const isLoading = ref<boolean>(false)

// 复杂类型
interface User {
  id: number
  name: string
  email: string
  age?: number
}

// 响应式对象
const user = reactive<User>({
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  age: 30
})

// 数组
const items = ref<string[]>(['Item 1', 'Item 2', 'Item 3'])
const users = ref<User[]>([
  { id: 1, name: '<PERSON>', email: '<EMAIL>' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>' }
])

// 标签数据
const tags = ref<Tag[]>([])
const loadingTags = ref<boolean>(false)

// 计算属性
const fullName = computed<string>(() => {
  return `${user.name} (${user.email})`
})

// 方法
const increment = (): void => {
  count.value++
  ElMessage.success(`Count incremented to ${count.value}`)
}

const addItem = (): void => {
  items.value.push(`Item ${items.value.length + 1}`)
}

const updateUser = (newName: string): void => {
  user.name = newName
}

// 异步方法
const fetchTags = async (): Promise<void> => {
  loadingTags.value = true
  try {
    const res = await getAllTags()
    if (res.code === 0) {
      tags.value = res.data.first || []
      ElMessage.success('Tags loaded successfully')
    } else {
      ElMessage.error(res.msg || 'Failed to load tags')
    }
  } catch (error) {
    console.error('Error fetching tags:', error)
    ElMessage.error('Failed to load tags')
  } finally {
    loadingTags.value = false
  }
}

// 刷新用户信息示例
const refreshUserInfo = async (): Promise<void> => {
  isLoading.value = true
  try {
    const userInfo = await userStore.refreshUserInfo()
    ElMessage.success('用户信息已更新')
    console.log('更新后的用户信息:', userInfo)
  } catch (error) {
    console.error('刷新用户信息失败:', error)
    ElMessage.error('刷新用户信息失败')
  } finally {
    isLoading.value = false
  }
}

// 事件处理
const handleUpdate = (value: string): void => {
  message.value = value
  ElMessage.info(`Received update: ${value}`)
}

const handleDelete = (id: number): void => {
  ElMessage.warning(`Delete item with ID: ${id}`)
}

// 生命周期钩子
onMounted(() => {
  console.log('Component mounted')
  fetchTags()
})
</script>

<template>
  <div class="typescript-example">
    <h1>TypeScript Example</h1>

    <div class="section">
      <h2>Basic Types</h2>
      <p>Count: {{ count }}</p>
      <p>Message: {{ message }}</p>
      <el-button type="primary" @click="increment">Increment</el-button>
    </div>

    <div class="section">
      <h2>Complex Types</h2>
      <p>User: {{ fullName }}</p>
      <p>Age: {{ user.age }}</p>
      <el-input v-model="user.name" placeholder="Change name"></el-input>
    </div>

    <div class="section">
      <h2>Arrays</h2>
      <ul>
        <li v-for="(item, index) in items" :key="index">{{ item }}</li>
      </ul>
      <el-button @click="addItem">Add Item</el-button>
    </div>

    <div class="section">
      <h2>API Integration</h2>
      <el-button type="success" @click="fetchTags" :loading="loadingTags">
        Fetch Tags
      </el-button>

      <div v-if="tags.length > 0" class="tags-container">
        <el-tag v-for="tag in tags" :key="tag.id" :color="tag.color" class="tag-item">
          {{ tag.name }}
        </el-tag>
      </div>
      <p v-else>No tags found</p>
    </div>

    <div class="section">
      <h2>Component Integration</h2>
      <ExampleComponent title="Example Component" :showHeader="true" :items="items" @update="handleUpdate" @delete="handleDelete" />
    </div>
  </div>
</template>

<style scoped>
.typescript-example {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.tags-container {
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-item {
  margin-right: 5px;
}
</style>
