<template>
  <div class="tags-container">
    <el-card class="tags-card" style="border-radius: 14px">
      <div class="tag-levels-container">
        <div class="level-tabs">
          <div
            class="tab-item"
            :class="{ active: activeTab === 1 }"
            @click="switchTab(1)"
          >
            <span>+添加第一层级</span>
          </div>
          <div
            class="tab-item"
            :class="{ active: activeTab === 2 }"
            @click="switchTab(2)"
          >
            <span>+添加第二层级</span>
          </div>
          <div
            class="tab-item"
            :class="{ active: activeTab === 3 }"
            @click="switchTab(3)"
          >
            <span>+添加第三层级</span>
          </div>
        </div>

        <div class="level-content">
          <div class="level-grid">
            <!-- 第一级 -->
            <div class="level-column">
              <div class="level-items">
                <div
                  class="level-item"
                  v-for="(item, index) in firstLevelTags"
                  :class="{ active: selectedLevel.first === index }"
                  :key="'first-' + item.id"
                  @click="selectTag(1, index, item.id)"
                >
                  <span>{{ item.name }}</span>
                  <div class="item-actions" v-if="editingLevel === 1">
                    <el-icon
                      class="edit-icon"
                      @click.stop="openEditDialog(1, item)"
                      ><Edit
                    /></el-icon>
                    <el-icon
                      class="delete-icon"
                      @click.stop="removeFirstLevel(index, item.id)"
                      ><Delete
                    /></el-icon>
                  </div>
                </div>
              </div>
              <div class="column-footer">
                <el-button class="edit-btn" @click="toggleEditLevel(1)">
                  {{ editingLevel === 1 ? "完成" : "编辑" }}
                  <el-icon class="el-icon--right">
                    <Edit v-if="editingLevel !== 1" />
                    <Check v-else />
                  </el-icon>
                </el-button>
              </div>
            </div>

            <!-- 第二级 -->
            <div class="level-column">
              <div class="level-items">
                <div
                  class="level-item"
                  v-for="(item, index) in secondLevelTags"
                  :class="{ active: selectedLevel.second === index }"
                  :key="'second-' + item.id"
                  @click="selectTag(2, index, item.id)"
                >
                  <span>{{ item.name }}</span>
                  <div class="item-actions" v-if="editingLevel === 2">
                    <el-icon
                      class="edit-icon"
                      @click.stop="openEditDialog(2, item)"
                      ><Edit
                    /></el-icon>
                    <el-icon
                      class="delete-icon"
                      @click.stop="removeSecondLevel(index, item.id)"
                      ><Delete
                    /></el-icon>
                  </div>
                </div>
              </div>
              <div class="column-footer">
                <el-button class="edit-btn" @click="toggleEditLevel(2)">
                  {{ editingLevel === 2 ? "完成" : "编辑" }}
                  <el-icon class="el-icon--right">
                    <Edit v-if="editingLevel !== 2" />
                    <Check v-else />
                  </el-icon>
                </el-button>
              </div>
            </div>

            <!-- 第三级 -->
            <div class="level-column">
              <div class="level-items">
                <div
                  class="level-item"
                  v-for="(item, index) in thirdLevelTags"
                  :class="{ active: selectedLevel.third === index }"
                  :key="'third-' + item.id"
                  @click="selectTag(3, index, item.id)"
                >
                  <span>{{ item.name }}</span>
                  <div class="item-actions" v-if="editingLevel === 3">
                    <el-icon
                      class="edit-icon"
                      @click.stop="openEditDialog(3, item)"
                      ><Edit
                    /></el-icon>
                    <el-icon
                      class="delete-icon"
                      @click.stop="removeThirdLevel(index, item.id)"
                      ><Delete
                    /></el-icon>
                  </div>
                </div>
              </div>
              <div class="column-footer">
                <el-button class="edit-btn" @click="toggleEditLevel(3)">
                  {{ editingLevel === 3 ? "完成" : "编辑" }}
                  <el-icon class="el-icon--right">
                    <Edit v-if="editingLevel !== 3" />
                    <Check v-else />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 添加标签对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="`${dialogType === 'add' ? '添加' : '编辑'}${
        levelText[dialogLevel - 1]
      }层级`"
      width="500px"
    >
      <el-form
        :model="tagForm"
        label-width="100px"
        :rules="formRules"
        ref="tagFormRef"
      >
        <el-form-item label="标签名称" prop="name">
          <el-input
            v-model="tagForm.name"
            placeholder="请输入标签名称"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="dialogLevel === 1" label="标签颜色" prop="color">
          <el-color-picker v-model="tagForm.color" show-alpha></el-color-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            style="background-color: #1bb394"
            @click="submitTag"
            :loading="submitLoading"
            >确定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getAllTags,
  addTag as apiAddTag,
  editTag as apiEditTag,
  deleteTag as apiDeleteTag,
} from "@/api/tag";

const tagFormRef = ref(null);

// 标签层级
const activeTab = ref(1);
const levelText = ["第一", "第二", "第三"];

// 当前选中的标签
const selectedLevel = reactive({
  first: -1,
  second: -1,
  third: -1,
});

// 选中的标签ID
const selectedTagIds = reactive({
  first: 0,
  second: 0,
  third: 0,
});

// 选择标签
const selectTag = (level, index, id) => {
  if (level === 1) {
    selectedLevel.first = index;
    selectedTagIds.first = id;
    // 加载对应的二级标签
    loadSecondLevelTags(id);
    // 清空三级标签选择
    selectedLevel.third = -1;
    selectedTagIds.third = 0;
  } else if (level === 2) {
    selectedLevel.second = index;
    selectedTagIds.second = id;
    // 加载对应的三级标签
    loadThirdLevelTags(id);
  } else if (level === 3) {
    selectedLevel.third = index;
    selectedTagIds.third = id;
  }
};

// 当前正在编辑的层级
const editingLevel = ref(0);

// 切换编辑状态
const toggleEditLevel = (level) => {
  if (editingLevel.value === level) {
    editingLevel.value = 0; // 关闭编辑
  } else {
    editingLevel.value = level; // 开启编辑
  }
};

// 对话框相关
const dialogVisible = ref(false);
const dialogType = ref("add"); // add 或 edit
const dialogLevel = ref(1);
const submitLoading = ref(false);
const editingTagId = ref(0);

// 标签表单
const tagForm = reactive({
  name: "",
  color: "#16b788",
  level: 1,
  parent_id: 0,
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: "请输入标签名称", trigger: "blur" }],
  color: [{ required: true, message: "请选择标签颜色", trigger: "change" }],
};

// 标签数据
const firstLevelTags = ref([]);
const secondLevelTags = ref([]);
const thirdLevelTags = ref([]);
const loading = ref(false);

// 切换标签页
const switchTab = (tab) => {
  if (tab === activeTab.value) {
    // 如果点击当前已选中的标签，则打开添加对话框
    openAddDialog(tab);
  } else {
    // 否则切换标签
    activeTab.value = tab;
  }
};

// 打开添加对话框
const openAddDialog = (level) => {
  dialogType.value = "add";
  dialogLevel.value = level;
  dialogVisible.value = true;

  // 重置表单
  tagForm.name = "";
  tagForm.color = "#16b788";
  tagForm.level = level;

  // 设置父级ID
  if (level === 1) {
    tagForm.parent_id = 0;
  } else if (level === 2) {
    if (selectedTagIds.first <= 0) {
      ElMessage.warning("请先选择一个一级标签");
      dialogVisible.value = false;
      return;
    }
    tagForm.parent_id = selectedTagIds.first;
  } else if (level === 3) {
    if (selectedTagIds.second <= 0) {
      ElMessage.warning("请先选择一个二级标签");
      dialogVisible.value = false;
      return;
    }
    tagForm.parent_id = selectedTagIds.second;
  }
};

// 打开编辑对话框
const openEditDialog = (level, item) => {
  dialogType.value = "edit";
  dialogLevel.value = level;
  editingTagId.value = item.id;

  // 设置表单数据
  tagForm.name = item.name;
  if (level === 1 && item.color) {
    tagForm.color = item.color;
  } else {
    tagForm.color = "#16b788";
  }

  dialogVisible.value = true;
};

// 删除标签
const removeFirstLevel = (index, id) => {
  ElMessageBox.confirm(
    `确定要删除标签 "${firstLevelTags.value[index].name}" 吗?`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      // 调用删除API
      apiDeleteTag(id)
        .then((res) => {
          if (res.code === 0) {
            // 从一级标签列表中删除
            firstLevelTags.value.splice(index, 1);

            // 更新选中状态
            if (selectedLevel.first === index) {
              selectedLevel.first = -1;
              selectedTagIds.first = 0;
              // 清空二级和三级标签
              secondLevelTags.value = [];
              thirdLevelTags.value = [];
            } else if (selectedLevel.first > index) {
              selectedLevel.first--;
            }

            // 从所有二级标签中删除以该标签为父级的标签
            allSecondLevelTags.value = allSecondLevelTags.value.filter(
              (item) => item.parent_id !== id
            );

            // 找出所有需要删除的二级标签ID
            const secondLevelIds = allSecondLevelTags.value
              .filter((item) => item.parent_id === id)
              .map((item) => item.id);

            // 从所有三级标签中删除以这些二级标签为父级的标签
            allThirdLevelTags.value = allThirdLevelTags.value.filter(
              (item) => !secondLevelIds.includes(item.parent_id)
            );

            ElMessage.success("删除成功");
          } else {
            ElMessage.error(res.msg || "删除失败");
          }
        })
        .catch((err) => {
          console.error(err);
          ElMessage.error("删除失败，请稍后重试");
        });
    })
    .catch(() => {});
};

const removeSecondLevel = (index, id) => {
  ElMessageBox.confirm(
    `确定要删除标签 "${secondLevelTags.value[index].name}" 吗?`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      // 调用删除API
      apiDeleteTag(id)
        .then((res) => {
          if (res.code === 0) {
            // 从当前显示的二级标签列表中删除
            secondLevelTags.value.splice(index, 1);

            // 从所有二级标签中删除
            const allIndex = allSecondLevelTags.value.findIndex(
              (item) => item.id === id
            );
            if (allIndex !== -1) {
              allSecondLevelTags.value.splice(allIndex, 1);
            }

            // 更新选中状态
            if (selectedLevel.second === index) {
              selectedLevel.second = -1;
              selectedTagIds.second = 0;
              // 清空三级标签
              thirdLevelTags.value = [];
            } else if (selectedLevel.second > index) {
              selectedLevel.second--;
            }

            // 从所有三级标签中删除以该标签为父级的标签
            allThirdLevelTags.value = allThirdLevelTags.value.filter(
              (item) => item.parent_id !== id
            );

            ElMessage.success("删除成功");
          } else {
            ElMessage.error(res.msg || "删除失败");
          }
        })
        .catch((err) => {
          console.error(err);
          ElMessage.error("删除失败，请稍后重试");
        });
    })
    .catch(() => {});
};

const removeThirdLevel = (index, id) => {
  ElMessageBox.confirm(
    `确定要删除标签 "${thirdLevelTags.value[index].name}" 吗?`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      // 调用删除API
      apiDeleteTag(id)
        .then((res) => {
          if (res.code === 0) {
            // 从当前显示的三级标签列表中删除
            thirdLevelTags.value.splice(index, 1);

            // 从所有三级标签中删除
            const allIndex = allThirdLevelTags.value.findIndex(
              (item) => item.id === id
            );
            if (allIndex !== -1) {
              allThirdLevelTags.value.splice(allIndex, 1);
            }

            // 更新选中状态
            if (selectedLevel.third === index) {
              selectedLevel.third = -1;
              selectedTagIds.third = 0;
            } else if (selectedLevel.third > index) {
              selectedLevel.third--;
            }

            ElMessage.success("删除成功");
          } else {
            ElMessage.error(res.msg || "删除失败");
          }
        })
        .catch((err) => {
          console.error(err);
          ElMessage.error("删除失败，请稍后重试");
        });
    })
    .catch(() => {});
};

// 提交添加/编辑标签
const submitTag = async () => {
  if (!tagFormRef.value) return;

  try {
    await tagFormRef.value.validate();
    submitLoading.value = true;

    // 构建提交数据
    const data = {
      name: tagForm.name,
      level: dialogLevel.value,
      parent_id: tagForm.parent_id,
    };

    // 一级标签需要颜色
    if (dialogLevel.value === 1) {
      data.color = tagForm.color;
    }

    if (dialogType.value === "add") {
      // 添加标签
      apiAddTag(data)
        .then((res) => {
          if (res.code === 0) {
            // 添加成功，更新本地数据
            const newTag = res.data;
            if (dialogLevel.value === 1) {
              firstLevelTags.value.push(newTag);
            } else if (dialogLevel.value === 2) {
              // 添加到所有二级标签中
              allSecondLevelTags.value.push(newTag);
              // 如果当前选中的一级标签是新标签的父级，则也添加到当前显示的二级标签中
              if (newTag.parent_id === selectedTagIds.first) {
                secondLevelTags.value.push(newTag);
              }
            } else {
              // 添加到所有三级标签中
              allThirdLevelTags.value.push(newTag);
              // 如果当前选中的二级标签是新标签的父级，则也添加到当前显示的三级标签中
              if (newTag.parent_id === selectedTagIds.second) {
                thirdLevelTags.value.push(newTag);
              }
            }
            ElMessage.success(
              `添加${levelText[dialogLevel.value - 1]}层级标签成功`
            );
          } else {
            ElMessage.error(res.msg || "添加失败");
          }
          submitLoading.value = false;
          dialogVisible.value = false;
        })
        .catch((err) => {
          console.error(err);
          ElMessage.error("添加失败，请稍后重试");
          submitLoading.value = false;
        });
    } else {
      // 编辑标签
      data.id = editingTagId.value;
      apiEditTag(data)
        .then((res) => {
          if (res.code === 0) {
            // 编辑成功，更新本地数据
            const updatedTag = res.data;
            if (dialogLevel.value === 1) {
              // 更新一级标签
              const index = firstLevelTags.value.findIndex(
                (item) => item.id === updatedTag.id
              );
              if (index !== -1) {
                firstLevelTags.value[index] = updatedTag;
              }
            } else if (dialogLevel.value === 2) {
              // 更新所有二级标签中的数据
              const allIndex = allSecondLevelTags.value.findIndex(
                (item) => item.id === updatedTag.id
              );
              if (allIndex !== -1) {
                allSecondLevelTags.value[allIndex] = updatedTag;
              }

              // 更新当前显示的二级标签
              const index = secondLevelTags.value.findIndex(
                (item) => item.id === updatedTag.id
              );
              if (index !== -1) {
                secondLevelTags.value[index] = updatedTag;
              }
            } else {
              // 更新所有三级标签中的数据
              const allIndex = allThirdLevelTags.value.findIndex(
                (item) => item.id === updatedTag.id
              );
              if (allIndex !== -1) {
                allThirdLevelTags.value[allIndex] = updatedTag;
              }

              // 更新当前显示的三级标签
              const index = thirdLevelTags.value.findIndex(
                (item) => item.id === updatedTag.id
              );
              if (index !== -1) {
                thirdLevelTags.value[index] = updatedTag;
              }
            }
            ElMessage.success(
              `编辑${levelText[dialogLevel.value - 1]}层级标签成功`
            );
          } else {
            ElMessage.error(res.msg || "编辑失败");
          }
          submitLoading.value = false;
          dialogVisible.value = false;
        })
        .catch((err) => {
          console.error(err);
          ElMessage.error("编辑失败，请稍后重试");
          submitLoading.value = false;
        });
    }
  } catch (error) {
    console.error("表单验证失败", error);
    submitLoading.value = false;
  }
};

// 加载所有标签
const loadAllTags = () => {
  loading.value = true;
  getAllTags()
    .then((res) => {
      if (res.code === 0) {
        // 保存所有标签数据
        firstLevelTags.value = res.data.first || [];
        // 保存所有二级和三级标签的原始数据，用于后续筛选
        allSecondLevelTags.value = res.data.second || [];
        allThirdLevelTags.value = res.data.third || [];

        // 初始时不显示二级和三级标签，需要用户先选择上级标签
        secondLevelTags.value = [];
        thirdLevelTags.value = [];
      } else {
        ElMessage.error(res.msg || "获取标签失败");
      }
      loading.value = false;
    })
    .catch((err) => {
      console.error(err);
      ElMessage.error("获取标签失败，请稍后重试");
      loading.value = false;
    });
};

// 存储所有二级和三级标签的原始数据
const allSecondLevelTags = ref([]);
const allThirdLevelTags = ref([]);

// 加载二级标签
const loadSecondLevelTags = (parentId) => {
  if (!parentId) return;

  // 从所有二级标签中筛选出父级ID匹配的标签
  secondLevelTags.value = allSecondLevelTags.value.filter(
    (item) => item.parent_id === parentId
  );

  // 清空三级标签
  thirdLevelTags.value = [];
  selectedLevel.second = -1;
  selectedTagIds.second = 0;

  console.log("加载二级标签", parentId, secondLevelTags.value);
};

// 加载三级标签
const loadThirdLevelTags = (parentId) => {
  if (!parentId) return;

  // 从所有三级标签中筛选出父级ID匹配的标签
  thirdLevelTags.value = allThirdLevelTags.value.filter(
    (item) => item.parent_id === parentId
  );

  selectedLevel.third = -1;
  selectedTagIds.third = 0;

  console.log("加载三级标签", parentId, thirdLevelTags.value);
};

// 监听tab变化
watch(activeTab, (newVal) => {
  if (newVal > 1 && firstLevelTags.value.length === 0) {
    ElMessage.warning("请先添加第一层级标签");
    activeTab.value = 1;
  } else if (newVal > 2 && !secondLevelTags.value.length) {
    ElMessage.warning("请先添加第二层级标签");
    activeTab.value = 2;
  }
});

// 页面加载时初始化数据
onMounted(() => {
  // 从API加载初始数据
  loadAllTags();
});
</script>

<style scoped>
.tags-container {
  padding: 20px;
  height: 100%;
}

.tags-card {
  width: 100%;
  height: 100%;
}

:deep(.el-card__body) {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tag-levels-container {
  width: 46%;
}

.level-tabs {
  display: flex;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  color: #16b788;
  border: 1px solid #16b788;

  &:first-child {
    border-radius: 12px 0 0 0;
    border-right: none;
  }

  &:last-child {
    border-left: none;
  }
}

.tab-item:last-child {
  border-right: none;
}

.tab-item.active {
  background-color: #16b788;
  color: white;
}

.level-content {
  width: 100%;
}

.level-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  height: 380px;
  overflow: hidden;
}

.level-column {
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.level-column:last-child {
  border-right: none;
}

.level-items {
  padding: 10px;
  flex: 1;
  overflow-y: auto;
}

.level-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #f5f7fa;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s;
  position: relative;
}

.level-item:hover {
  background-color: #e6f7f1;
}

.level-item.active {
  background-color: #16b788;
  color: white;
}

.level-item.active .edit-icon {
  color: white;
}

.level-item.active .delete-icon {
  color: white;
}

.item-actions {
  display: flex;
  gap: 8px;
}

.edit-icon {
  color: #409eff;
  cursor: pointer;
}

.delete-icon {
  color: #f56c6c;
  cursor: pointer;
}

.column-footer {
  padding: 10px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.edit-btn {
  background-color: #16b788;
  border-color: #e9f8f4;
  color: #fff;
  border-radius: 12px;
  text-align: center;
  width: 100%;
  height: 50px;
  line-height: 50px;
}

.confirm-btn {
  background-color: #16b788;
  border-color: #16b788;
}
</style>
