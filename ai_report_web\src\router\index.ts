import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { RouteMeta } from '@/types'
import { checkSpiderLogin } from '@/api/report'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/index.vue'),
    meta: { title: '登录', isAuth: false }
  },

  {
    path: '/404',
    name: 'NotFound',
    component: () => import('../views/error/404.vue'),
    meta: { title: '页面丢失', isAuth: false }
  },
  {
    path: '/',
    component: () => import('../layout/index.vue'),
    redirect: '/report/generate',
    children: [
      {
        path: 'report/generate',
        name: 'ReportGenerate',
        component: () => import('../views/report/generate.vue'),
        meta: { title: '生成报告', icon: 'Document' }
      },
      {
        path: 'report/list',
        name: 'ReportList',
        component: () => import('../views/report/report-list.vue'),
        meta: { title: '报告列表', icon: 'Document' }
      },
      {
        path: 'student/list',
        name: 'StudentList',
        component: () => import('../views/student/list.vue'),
        meta: { title: '学员列表', icon: 'DataLine' }
      },
      {
        path: 'student/tags',
        name: 'StudentTags',
        component: () => import('../views/student/tags.vue'),
        meta: { title: '标签管理', icon: 'DataLine' }
      },
      {
        path: 'example/typescript',
        name: 'TypeScriptExample',
        component: () => import('../views/example/TypeScriptExample.vue'),
        meta: { title: 'TypeScript示例', icon: 'Document' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: "/404"
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from) => {
  document.title = to.meta.title ? `${to.meta.title} - AI择校报告管理系统` : 'AI择校报告管理系统'

  // 获取token
  const token = localStorage.getItem('token')
  
  // 定义不需要登录的白名单路径
  const whiteList = ['/login', '/404']
  
  // 如果存在token且访问登录页面，直接跳转到生成报告页面
  if (token && to.path === '/login') {
    console.log('用户已登录，跳转到生成报告页面')
    return '/report/generate'
  }

  // 如果没有token
  if (!token) {
    // 检查是否在白名单中
    if (whiteList.includes(to.path)) {
      // 在白名单中，直接放行
      return true
    } else {
      // 不在白名单中，跳转到登录页
      return '/login'
    }
  } // 其他页面直接放行
    return true
})

export default router
