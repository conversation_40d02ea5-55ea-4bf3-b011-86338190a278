import COS from "cos-js-sdk-v5";
import http from "./request";

// 定义类型接口
interface UploadResult {
  Location: string;
  Bucket: string;
  Key: string;
  ETag: string;
  [key: string]: any;
}

interface ProgressData {
  loaded: number;
  total: number;
  speed: number;
  percent: number;
}

/**
 * 生成PDF并上传到COS
 *
 * 注意：当前实现为临时方案，生成文本文件而非PDF
 *
 * 要实现真正的PDF生成，请按以下步骤操作：
 * 1. 安装依赖：npm install jspdf html2canvas @types/jspdf
 * 2. 取消注释下面的PDF生成代码
 * 3. 删除当前的临时文本文件生成代码
 */
export function generateAndUploadPDF(
  htmlContent: string,
  filename: string = "report.pdf"
): Promise<UploadResult> {
  return new Promise(async (resolve, reject) => {
    try {
      // 创建一个可见的临时容器来渲染内容
      const tempContainer = document.createElement('div');
      // 将容器设置为固定定位，在可见区域但层级较低
      tempContainer.style.position = 'fixed';
      tempContainer.style.left = '0';
      tempContainer.style.top = '0';
      tempContainer.style.width = '1200px';
      tempContainer.style.height = 'auto';
      tempContainer.style.backgroundColor = 'white';
      tempContainer.style.zIndex = '-9999';  // 设置层级较低，不影响用户界面
      tempContainer.style.overflow = 'visible';
      tempContainer.style.opacity = '0';  // 设置透明但保持可渲染

      // 处理HTML内容，移除保存按钮
      let processedHtmlContent = htmlContent;

      // 移除保存按钮相关的内容
      processedHtmlContent = processedHtmlContent.replace(/<div[^>]*class[^>]*save-report-container[^>]*>[\s\S]*?<\/div>/gi, '');
      processedHtmlContent = processedHtmlContent.replace(/<button[^>]*保存报告[^>]*>[\s\S]*?<\/button>/gi, '');

      tempContainer.innerHTML = processedHtmlContent;

      // 将容器添加到body中
      document.body.appendChild(tempContainer);

      try {
        // 等待DOM渲染完成
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 查找并重新初始化所有图表容器
        const chartContainers = tempContainer.querySelectorAll('[id*="preview-"], .echarts-box, .preview-chart');
        console.log('找到的图表容器数量:', chartContainers.length);

        // 动态导入echarts
        const echarts = await import('echarts');

        // 重新创建图表
        for (let i = 0; i < chartContainers.length; i++) {
          const container = chartContainers[i] as HTMLElement;
          console.log(`处理第${i + 1}个图表容器:`, container.id || container.className);

          // 设置容器样式确保可见
          container.style.width = '100%';
          container.style.height = '400px';
          container.style.display = 'block';
          container.style.visibility = 'visible';
          container.style.opacity = '1';
          container.style.backgroundColor = '#fff';

          // 等待容器就绪
          await new Promise(resolve => setTimeout(resolve, 100));

          try {
            // 尝试从多个来源获取图表配置
            let chartOption = null;
            
            // 方法1：从原始页面的对应图表获取配置
            const originalContainer = document.querySelector(`#${container.id}`) || 
                                     document.querySelector(`.${container.className.split(' ')[0]}`);
            
            if (originalContainer && (originalContainer as any)._echarts_instance_) {
              chartOption = (originalContainer as any)._echarts_instance_.getOption();
              console.log(`从原始图表获取配置成功: ${container.id}`);
            } 
            // 方法2：如果是预览中的图表，尝试从预览容器获取实例
            else {
              // 查找所有现有的图表实例，按顺序匹配
              const allChartElements = document.querySelectorAll('[_echarts_instance_]');
              if (allChartElements.length > i && (allChartElements[i] as any)._echarts_instance_) {
                chartOption = (allChartElements[i] as any)._echarts_instance_.getOption();
                console.log(`从第${i + 1}个现有图表获取配置成功`);
              }
            }

            // 方法3：如果还没有找到配置，尝试使用默认配置
            if (!chartOption) {
              console.log(`未找到图表配置，使用默认配置: ${container.id}`);
              
              // 生成一个基础的图表配置
              chartOption = {
                title: {
                  text: '图表数据加载中...',
                  left: 'center',
                  textStyle: {
                    fontSize: 16,
                    color: '#333'
                  }
                },
                tooltip: {},
                xAxis: {
                  type: 'category',
                  data: ['暂无数据']
                },
                yAxis: {
                  type: 'value'
                },
                series: [{
                  name: '数据',
                  type: 'line',
                  data: [0]
                }]
              };
            }

            // 创建新的图表实例
            if (chartOption) {
              // 清理可能存在的旧实例
              if ((container as any)._echarts_instance_) {
                (container as any)._echarts_instance_.dispose();
              }

              const chart = echarts.init(container);
              chart.setOption(chartOption);
              
              // 保存实例引用
              (container as any)._echarts_instance_ = chart;
              
              console.log(`第${i + 1}个图表创建成功`);
              
              // 等待图表渲染
              await new Promise(resolve => setTimeout(resolve, 300));
              
              // 强制重新渲染图表
              chart.resize();
              await new Promise(resolve => setTimeout(resolve, 200));
            }
          } catch (error) {
            console.error(`创建第${i + 1}个图表失败:`, error);
            
            // 如果创建失败，至少确保容器有一些内容
            try {
              const chart = echarts.init(container);
              chart.setOption({
                title: {
                  text: '图表渲染异常',
                  left: 'center',
                  textStyle: {
                    fontSize: 14,
                    color: '#999'
                  }
                }
              });
              (container as any)._echarts_instance_ = chart;
            } catch (fallbackError) {
              console.error(`创建备用图表也失败:`, fallbackError);
            }
          }
        }

        // 给图表更多时间渲染
        await new Promise(resolve => setTimeout(resolve, 2000));

        console.log('开始截图，容器尺寸:', tempContainer.scrollWidth, 'x', tempContainer.scrollHeight);
        
        // 验证容器内容是否足够
        const containerText = tempContainer.textContent || tempContainer.innerText || '';
        const hasCharts = tempContainer.querySelectorAll('[_echarts_instance_]').length > 0;
        const hasContent = containerText.length > 100; // 至少要有100个字符的内容
        
        console.log('内容验证:', {
          textLength: containerText.length,
          hasCharts: hasCharts,
          hasContent: hasContent,
          containerHeight: tempContainer.scrollHeight
        });
        
        if (!hasContent && !hasCharts) {
          console.warn('警告：容器内容可能不足，可能导致空白PDF');
        }

        // 使用html2canvas截图
        const canvas = await import('html2canvas').then(module => {
          const html2canvas = module.default;
          return html2canvas(tempContainer, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            width: tempContainer.scrollWidth || 1200,
            height: tempContainer.scrollHeight || 800,
            logging: false,
            removeContainer: false,
            foreignObjectRendering: true,
            imageTimeout: 30000,
            onclone: (clonedDoc: Document) => {
              console.log('开始克隆文档');
              const clonedContainer = clonedDoc.querySelector('div');
              if (clonedContainer) {
                (clonedContainer as HTMLElement).style.width = '1200px';
                (clonedContainer as HTMLElement).style.display = 'block';
                (clonedContainer as HTMLElement).style.visibility = 'visible';
                (clonedContainer as HTMLElement).style.opacity = '1';

                // 确保所有元素可见
                const allElements = clonedContainer.querySelectorAll('*');
                allElements.forEach((el: any) => {
                  if (el.style) {
                    el.style.visibility = 'visible';
                    el.style.opacity = '1';
                    if (el.style.display === 'none') {
                      el.style.display = 'block';
                    }
                  }
                });

                // 特别处理图表容器
                const chartElements = clonedContainer.querySelectorAll('[id*="preview-"], .echarts-box, .preview-chart');
                chartElements.forEach((container: any) => {
                  container.style.width = '100%';
                  container.style.height = '400px';
                  container.style.display = 'block';
                  container.style.visibility = 'visible';
                  container.style.opacity = '1';
                  container.style.backgroundColor = '#fff';
                });

                console.log('克隆容器设置完成');
              }
            }
          });
        });

        // 验证canvas
        if (!canvas || canvas.width === 0 || canvas.height === 0) {
          throw new Error('Canvas生成失败或尺寸无效');
        }

        const imgData = canvas.toDataURL('image/jpeg', 0.95);

        if (!imgData || imgData === 'data:,' || imgData.length < 100) {
          throw new Error('图片数据生成失败');
        }

        console.log('Canvas尺寸:', canvas.width, 'x', canvas.height);
        console.log('图片数据长度:', imgData.length);

        // 检查图片数据是否有效
        if (imgData.length < 1000) {
          throw new Error('图片数据太小，可能截图失败');
        }

        // 使用jsPDF生成PDF
        const { jsPDF } = await import('jspdf');
        const pdf = new jsPDF('l', 'mm', 'a3');

        const imgWidth = 420;
        const pageHeight = 297;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;
        let position = 0;

        console.log('PDF参数:', { imgWidth, pageHeight, imgHeight, heightLeft });

        // 添加第一页
        pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // 如果内容超过一页，添加更多页面
        while (heightLeft >= 0) {
          position = heightLeft - imgHeight;
          pdf.addPage();
          pdf.addImage(imgData, 'JPEG', 0, position, imgWidth, imgHeight);
          heightLeft -= pageHeight;
        }

        console.log('PDF生成完成，页数:', pdf.getNumberOfPages());

        // 生成PDF Blob
        const pdfBlob = pdf.output('blob');

        // 创建File对象
        const file = new File([pdfBlob], filename, {
          type: "application/pdf",
        });

        // 上传到COS
        upload(file).then(resolve).catch(reject);

      } catch (error) {
        console.error('PDF生成失败，使用备用方案:', error);

        // 备用方案1：尝试简单的文本PDF
        try {
          const { jsPDF } = await import('jspdf');
          const pdf = new jsPDF('p', 'mm', 'a4');

          // 提取文本内容
          const textContent = tempContainer.textContent || tempContainer.innerText || '报告内容';
          const lines = textContent.split('\n').filter(line => line.trim());

          // 添加文本到PDF
          pdf.setFontSize(12);
          let yPosition = 20;
          const lineHeight = 7;
          const maxWidth = 180;

          lines.forEach(line => {
            if (yPosition > 280) {
              pdf.addPage();
              yPosition = 20;
            }

            const splitLines = pdf.splitTextToSize(line, maxWidth);
            splitLines.forEach((splitLine: string) => {
              if (yPosition > 280) {
                pdf.addPage();
                yPosition = 20;
              }
              pdf.text(splitLine, 15, yPosition);
              yPosition += lineHeight;
            });
          });

          const pdfBlob = pdf.output('blob');
          const file = new File([pdfBlob], filename, {
            type: "application/pdf",
          });

          upload(file).then(resolve).catch(reject);

        } catch (textPdfError) {
          console.error('文本PDF生成也失败，使用HTML备用方案:', textPdfError);

          // 备用方案2：生成HTML文件
          const htmlBlob = new Blob([`
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="UTF-8">
              <title>报告</title>
              <style>
                body {
                  font-family: Arial, sans-serif;
                  margin: 20px;
                  line-height: 1.6;
                }
                .report-container {
                  max-width: 800px;
                  margin: 0 auto;
                }
                @media print {
                  body { margin: 0; }
                  .no-print { display: none; }
                }
              </style>
            </head>
            <body>
              <div class="report-container">
                ${htmlContent}
              </div>
            </body>
            </html>
          `], { type: "text/html" });

          const file = new File([htmlBlob], filename.replace('.pdf', '.html'), {
            type: "text/html",
          });

          upload(file).then(resolve).catch(reject);
        }
      } finally {
        // 清理临时容器
        if (tempContainer.parentNode) {
          document.body.removeChild(tempContainer);
        }
      }
    } catch (error) {
      reject(error);
    }
  });
}

// 上传文件，file为选择的文件
export function upload(file: File): Promise<UploadResult> {
  return new Promise((resolve, reject) => {
    http
      .get("/coskey", { params: { filename: file.name } })
      .then((response: any) => {

        const data = response.data || response;
        if (data.code == 0) {
          return reject(data.msg);
        }

        console.log("获取上传路径和临时密钥成功", data);

        // 服务端接口需要返回：上传的存储桶、地域、随机路径的对象键、临时密钥
        // 在返回值里取临时密钥信息，上传的文件路径信息
        const {
          TmpSecretId,
          TmpSecretKey,
          SessionToken,
          StartTime,
          ExpiredTime,
          Bucket,
          Region,
          Key,
        } = data;

        // 创建 JS SDK 实例，传入临时密钥参数
        const cos = new COS({
          SecretId: TmpSecretId,
          SecretKey: TmpSecretKey,
          SecurityToken: SessionToken,
          StartTime,
          ExpiredTime,
        });

        // 上传文件
        cos.uploadFile(
          {
            Bucket,
            Region,
            Key, // 上传到 COS 的文件路径，包含文件名
            Body: file, // 要上传的文件对象
            onProgress: function (progressData: ProgressData) {
              console.log("上传进度：", progressData);
            },
            ContentType: file.type, // 上传文件的类型
          },
          function (err: any, data: any) {
            console.log("上传结束", err || data);
            if (err) {
              reject(err);
            } else {
              resolve(data as UploadResult);
            }
          }
        );
      })
      .catch((error: Error) => {
        console.error("获取上传路径和临时密钥失败", error);
        reject(error);
      });
  });
}
