import request from '@/utils/request'
import { ApiResponse } from '@/types'

interface LoginData {
  username: string;
  password: string;
}

interface UserInfo {
  id: number | string;
  username: string;
  nickname: string;
  avatar?: string;
  roles: string[];
  permissions?: string[];
  [key: string]: any;
}

interface TeacherListParams {
  page?: number;
  limit?: number;
  keyword?: string;
}

export interface Teacher {
  id: number | string;
  username: string;
  nickname: string;
  mobile?: string;
  avatar?: string;
}

/**
 * 用户登录
 * @param data - 登录信息
 * @returns Promise
 */
export function login(data: LoginData): Promise<ApiResponse> {
  return request({
    url: '/login',
    method: 'post',
    data
  })
}

/**
 * 获取用户信息
 * @returns Promise
 */
export function getUserInfo(): Promise<ApiResponse<UserInfo>> {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

/**
 * 退出登录
 * @returns Promise
 */
export function logout(): Promise<ApiResponse> {
  return request({
    url: '/logout',
    method: 'post'
  })
}

/**
 * 获取老师列表
 * @param params - 查询参数
 * @returns Promise
 */
export function getTeacherList(params: TeacherListParams = {}): Promise<ApiResponse<Teacher[]>> {
  return request({
    url: '/user/teacher-list',
    method: 'get',
    params
  })
}

/**
 * 搜索教师
 * @param keyword - 搜索关键词
 * @returns Promise
 */
export function searchTeacher(keyword: string): Promise<ApiResponse<Teacher[]>> {
  return request({
    url: '/user/search-teacher',
    method: 'get',
    params: { keyword }
  })
}

/**
 * 验证token有效性
 * @returns Promise
 */
export function verifyToken(): Promise<ApiResponse> {
  return request({
    url: '/user/verify-token',
    method: 'get'
  })
}

export default {
  login,
  getUserInfo,
  logout,
  getTeacherList,
  searchTeacher,
  verifyToken
}